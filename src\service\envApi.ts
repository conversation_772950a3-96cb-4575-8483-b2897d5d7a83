import { envRequest } from '@/request';

interface OptionalProps {
  [key: string]: string | number | boolean | object[] | null;
}

export type ResponseType = {
  data: OptionalProps;
  code: number;
  msg: string;
};

export type ResponsPageType = {
  data: Record<string, unknown>[];
  code: number;
  msg: string;
  current: number;
  pageSize: number;
  total: number;
};

type queryDataType = Record<string, string | number | boolean | object> | object[] | unknown;

/**
 * 根据 dictKey 判断有设备的公司数据
 * @param dictKey
 * @returns
 */
const queryUnitInfoByType: (dictKey: string) => Promise<ResponseType> = (
  dictKey: string,
): Promise<ResponseType> => {
  return envRequest.post('/monitor/queryUnitInfoByType', {
    data: {
      type: dictKey,
      monitorOnline: 0,
    },
  });
};

/**
 * 根据选择公司获取设备
 * @param params
 * @returns
 */
const queryEquipInfoByCompanyId: (data: {
  companyId: string;
  monitorType: string;
  type: string;
  monitorOnline?: string;
}) => Promise<ResponseType> = (data: {
  companyId: string;
  monitorType: string;
  type: string;
  monitorOnline?: string;
}): Promise<ResponseType> => {
  return envRequest.post('/monitor/queryEquipInfoByCompanyId', {
    data,
  });
};

/**
 * 获取表头column数据
 * @param equipId
 * @returns
 */
const queryMonitorIndex: (equipId: string, frequency: string) => Promise<ResponsPageType> = (
  equipId: string,
  frequency: string,
): Promise<ResponsPageType> => {
  return envRequest.get(`/monitor/queryMonitorIndex?equipId=${equipId}&frequency=${frequency}`);
};

/**
 * 在线监测-获取tableList数据
 * @param data
 * @returns
 */
const queryMonitorIndexValueItem: (data: {
  monitorType: string;
  equipCd: string;
  equipId: string;
  orgCd: string;
  startTm: string;
  endTm: string;
  dateType?: string | number;
  [key: string]: string | number;
}) => Promise<ResponseType> = (data) => {
  return envRequest.post(`/monitor/queryMonitorIndexValueItem`, { data });
};

/**
 * 在线监测-报表页面tableList
 * @param data
 * @returns
 */
const queryMonitorValueDayReport: (data: queryDataType) => Promise<ResponseType> = (
  data: queryDataType,
): Promise<ResponseType> => {
  return envRequest.post(`/monitor/queryMonitorValueDayReport`, { data });
};
/**
 * 在线监测-文件导出
 * @param data
 * @returns
 */
const exportMonitorData: (data: Record<string, string | unknown>) => Promise<BlobPart> = (
  data: Record<string, string | unknown>,
): Promise<BlobPart> => {
  return envRequest.post(`/monitor/exportMonitorData`, { ...data });
};
/**
 * 手工填报-tableList数据
 * @param data
 * @returns
 */
const queryManualFillTableList: (data: queryDataType) => Promise<ResponsPageType> = (
  data: queryDataType,
): Promise<ResponsPageType> => {
  return envRequest.post(`/qualityMonitor/page`, { data });
};
/**
 * 手工填报-删除列表数据
 * @param equipId
 * @returns
 */
const deleteManualFillById: (id: string) => Promise<ResponseType> = (
  id: string,
): Promise<ResponseType> => {
  return envRequest.delete(`/qualityMonitor/removeById?id=${id}`);
};

/**
 * 手工填报-报表-tableList
 * @param data
 * @returns
 */
const queryMonitorIndexReportData: (data: queryDataType) => Promise<ResponseType> = (
  data: queryDataType,
): Promise<ResponseType> => {
  return envRequest.post(`/qualityMonitor/queryMonitorIndexReportData`, { data });
};
/**
 * 手工填报-文件导出
 * @param data
 * @returns
 */
const exportManualFillData: (data: Record<string, unknown>) => Promise<BlobPart> = (
  data: Record<string, unknown>,
): Promise<BlobPart> => {
  return envRequest.post(`/qualityMonitor/exportMonitorData`, { ...data });
};
/**
 * 手工填报-查询填报详情
 * @param params
 * @returns
 */
const queryMonitorIndexInfoById: (params: { id: string }) => Promise<ResponseType> = (params: {
  id: string;
}): Promise<ResponseType> => {
  return envRequest.post(`/qualityMonitor/queryMonitorIndexInfo`, { params });
};
/**
 * 手工填报-新增
 * @param data
 * @returns
 */
const insertManualFillData: (data: queryDataType) => Promise<ResponseType> = (
  data: queryDataType,
): Promise<ResponseType> => {
  return envRequest.post(`/qualityMonitor/insert`, { data });
};
/**
 * 手工填报-编辑
 * @param data
 * @returns
 */
const updateManualFillData: (data: queryDataType) => Promise<ResponseType> = (
  data: queryDataType,
): Promise<ResponseType> => {
  return envRequest.put(`/qualityMonitor/update`, { data });
};
/**
 * 手工填报-下载模板
 * @param data
 * @returns
 */
const downloadManualFilltemplate: (data: queryDataType) => Promise<BlobPart> = (
  data: queryDataType,
): Promise<BlobPart> => {
  return envRequest.post(`/qualityMonitor/download`, { data });
};
/**
 * 手工填报-导入数据
 * @param data
 * @returns
 */
const importDataManualFill: (data: queryDataType) => Promise<ResponseType> = (
  data: queryDataType,
): Promise<ResponseType> => {
  return envRequest.post(`/qualityMonitor/importData`, { data });
};
/**
 * 首页-获取监测数量数据
 * @param data
 * @returns
 */
const queryEquipPlugInfo: (data: queryDataType) => Promise<{
  data: Record<string, number>;
  code: number;
  msg: string;
}> = (data: queryDataType) => {
  return envRequest.post(`/monitor/queryEquipPlugInfo`, { data });
};
/**
 * 首页-获取报警数量数据
 * @param data
 * @returns
 */
const queryAlarmNumByType: (data: queryDataType) => Promise<{
  data: Record<string, number>;
  code: number;
  msg: string;
}> = (data: queryDataType) => {
  return envRequest.post(`/envAlarm/queryAlarmNumByType`, { data });
};
/**
 * 首页-获取设备数据、接入、掉线、在线
 * @param data
 * @returns
 */
const queryMonitorEquipList: (data: queryDataType) => Promise<{
  data: Record<string, string | number>[];
  code: number;
  msg: string;
}> = (data: queryDataType) => {
  return envRequest.post(`/oneMap/queryMonitorEquipList`, { data });
};
/**
 * 首页-点击设备查询实时监测数据
 * @param data
 * @returns
 */
const queryMonitorRealtimeData: (data: queryDataType) => Promise<{
  data: {
    headList: onLineMonitorTypes.objType[];
    monitorEquipInfo: Record<string, string | number>;
    monitorValues: onLineMonitorTypes.objType[];
  };
  code: number;
  msg: string;
}> = (data: queryDataType) => {
  return envRequest.post(`/oneMap/queryMonitorRealtimeData`, { data });
};

/**
 * 首页-点击监测指标查询
 * @param data
 * @returns
 */
const queryMonitorIndexDetail: (data: queryDataType) => Promise<{
  data: Record<string, string | number>[];
  code: number;
  msg: string;
}> = (data: queryDataType) => {
  return envRequest.post(`/oneMap/queryMonitorIndexDetail`, { data });
};
/**
 * 首页-点击报警数量查询
 * @param data
 * @returns
 */
const queryEnvAlarmPage: (data: queryDataType) => Promise<ResponsPageType> = (
  data: queryDataType,
): Promise<ResponsPageType> => {
  return envRequest.post(`/envAlarm/page`, { data });
};
/**
 * 基础信息管理-查询环境空气质量监测tableList数据
 * @param data
 * @returns
 */
const queryEnvQualityMonitorList: (data: queryDataType) => Promise<ResponsPageType> = (
  data: queryDataType,
): Promise<ResponsPageType> => {
  return envRequest.post(`/monitor/page`, { data });
};
/**
 * 基础信息管理-删除tableList数据
 * @param id
 * @returns
 */
const deleteEnvQualityById: (id: string) => Promise<ResponseType> = (
  id: string,
): Promise<ResponseType> => {
  return envRequest.delete(`/monitor/removeById?id=${id}`);
};
/**
 * 基础信息管理-删除环境空气质量监测详情中的监测指标
 * @param id
 * @returns
 */
const deleteEnvQualityMonitorById: (id: string) => Promise<ResponseType> = (
  id: string,
): Promise<ResponseType> => {
  return envRequest.delete(`/envmonitor-index/remove?id=${id}`);
};
/**
 * 基础信息管理-环境空气质量监测详情
 * @param params
 * @returns
 */
const queryEnvQualityDetail: (params: { id: string }) => Promise<ResponseType> = (params: {
  id: string;
}): Promise<ResponseType> => {
  return envRequest.post(`/monitor/detail`, { params });
};
/**
 * 基础信息管理-更新环境空气质量监测
 * @param data
 * @returns
 */
const updateEnvQualityDetail: (data: queryDataType) => Promise<ResponseType> = (
  data: queryDataType,
): Promise<ResponseType> => {
  return envRequest.put(`/monitor/update`, { data });
};
/**
 * 基础信息管理-新增环境空气质量监测
 * @param data
 * @returns
 */
const insertEnvQualityDetail: (data: queryDataType) => Promise<ResponseType> = (
  data: queryDataType,
): Promise<ResponseType> => {
  return envRequest.post(`/monitor/insert`, { data });
};

/**
 * 报警管理-查询报警tableList
 * @param data
 * @returns
 */
const queryAlarmPageList: (data: queryDataType) => Promise<ResponsPageType> = (
  data: queryDataType,
): Promise<ResponsPageType> => {
  return envRequest.post(`/envAlarm/page`, { data });
};
/**
 * 报警管理-报警数据处置
 * @param data
 * @returns
 */
const alarmDispose: (data: queryDataType) => Promise<ResponseType> = (
  data: queryDataType,
): Promise<ResponseType> => {
  return envRequest.post(`/envAlarm/alarmDispose`, { data });
};

/**
 * 溯源管理-查询溯源数据列表
 * @param data
 * @returns
 */
const queryTraceabilityList: (data: queryDataType) => Promise<ResponsPageType> = (
  data: queryDataType,
): Promise<ResponsPageType> => {
  return envRequest.post(`/traceability/page`, { data });
};

/**
 * 溯源管理-查询溯源详情
 * @param data
 * @returns
 */
const queryTraceabilityDetail: (data: { id: string }) => Promise<ResponseType> = (data: {
  id: string;
}): Promise<ResponseType> => {
  return envRequest.post(`/traceability/detail`, { data });
};

/**
 * 溯源管理-溯源分析
 * @param data
 * @returns
 */
const analyzeTraceability: (data: queryDataType) => Promise<ResponseType> = (
  data: queryDataType,
): Promise<ResponseType> => {
  return envRequest.post(`/traceability/analyze`, { data });
};

/**
 * 溯源管理-导出溯源报告
 * @param data
 * @returns
 */
const exportTraceabilityReport: (data: queryDataType) => Promise<BlobPart> = (
  data: queryDataType,
): Promise<BlobPart> => {
  return envRequest.post(`/traceability/exportReport`, { data });
};

export {
  queryUnitInfoByType,
  queryEquipInfoByCompanyId,
  queryMonitorIndex,
  queryMonitorIndexValueItem,
  queryMonitorValueDayReport,
  exportMonitorData,
  queryManualFillTableList,
  deleteManualFillById,
  queryMonitorIndexReportData,
  exportManualFillData,
  queryMonitorIndexInfoById,
  updateManualFillData,
  insertManualFillData,
  downloadManualFilltemplate,
  importDataManualFill,
  queryEquipPlugInfo,
  queryAlarmNumByType,
  queryMonitorEquipList,
  queryMonitorRealtimeData,
  queryMonitorIndexDetail,
  queryEnvAlarmPage,
  queryEnvQualityMonitorList,
  deleteEnvQualityById,
  queryEnvQualityDetail,
  updateEnvQualityDetail,
  deleteEnvQualityMonitorById,
  insertEnvQualityDetail,
  queryAlarmPageList,
  alarmDispose,
  queryTraceabilityList,
  queryTraceabilityDetail,
  analyzeTraceability,
  exportTraceabilityReport,
};
